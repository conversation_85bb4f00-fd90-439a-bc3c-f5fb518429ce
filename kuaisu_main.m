clc;clear;
%==========================================================================
%                    第一章：参数设置与模型构建
%==========================================================================
% 功能说明：定义声波测井正演模拟的所有基本参数，包括介质物性、网格离散、
% 仪器配置、计算域设计等，并初始化所有场变量和材料分布。
%==========================================================================

%--------------------------------------------------------------------------
%                    1.1 物理参数与网格离散设计
%--------------------------------------------------------------------------

% 1.1.1 介质物理参数定义
% 模型结构： |──地层──||─井孔─||─地层─|─侵入带─|
% 井孔介质参数（泥浆）
po1=1.0*10^3;    % 密度 [kg/m^3]
vp1=1500;        % 纵波速度 [m/s]
vs1=0;           % 横波速度 [m/s] （流体）
% 地层介质参数（围岩）
po2=2.3*10^3;    % 密度 [kg/m^3]
vp2=4500;        % 纵波速度 [m/s]
vs2=2300;        % 横波速度 [m/s]
% 震源频率参数
f0=10*10^3;      % 主频率 [Hz]
% 井孔几何参数
cal=0.1;         % 井径 [m]

% 1.1.2 网格离散化参数计算
la1=vp1/f0;      % 波长计算 [m]
vmax=4500;       % 最大传播速度 [m/s]
n1=9/8;          % 四阶差分系数
n2=-1/24;        % 四阶差分系数
dx=la1/10;       % X方向空间步长 [m] （波长/10）
dz=la1/10;       % Z方向空间步长 [m]
dt=dx/(1.7321*vmax*(abs(n1)+abs(n2))); % 时间步长 [s] （CFL条件）

% 1.1.3 声波测井仪器几何参数
num_s=67;        % 模拟炮数（仪器整体长度）
L_StoR=1.5;      % 源距 [m] （震源到第一个接收器）
L_RtoR=0.15;     % 接收器间距 [m]
len_StoR=fix(L_StoR/dz);  % 源距网格点数
len_RtoR=fix(L_RtoR/dz);  % 接收器间距网格点数

% 1.1.4 计算域尺寸设计
pml=50;          % PML吸收边界层厚度 [网格点]
nx=2*pml+200;    % X方向总网格数 (物理尺寸: 4.5m)
nz=2*pml+1200;   % Z方向总网格数 (物理尺寸: 19.5m)

%--------------------------------------------------------------------------
%                    1.2 场变量内存分配与初始化
%--------------------------------------------------------------------------

% 1.2.1 速度场分量内存分配
% X方向速度分量（主场 + PML辅助场）
Vx=zeros(nz,nx);      % 主速度场
Vx_1=zeros(nz,nx);    % PML辅助场（X方向）
Vx_3=zeros(nz,nx);    % PML辅助场（Z方向）
% Z方向速度分量（主场 + PML辅助场）
Vz=zeros(nz,nx);      % 主速度场
Vz_1=zeros(nz,nx);    % PML辅助场（X方向）
Vz_3=zeros(nz,nx);    % PML辅助场（Z方向）

% 1.2.2 应力场分量内存分配
% 正应力分量Tao_xx（主场 + PML辅助场）
Tao_xx=zeros(nz,nx);    % 主应力场
Tao_xx_1=zeros(nz,nx);  % PML辅助场（X方向）
Tao_xx_3=zeros(nz,nx);  % PML辅助场（Z方向）
% 正应力分量Tao_zz（主场 + PML辅助场）
Tao_zz=zeros(nz,nx);    % 主应力场
Tao_zz_1=zeros(nz,nx);  % PML辅助场（X方向）
Tao_zz_3=zeros(nz,nx);  % PML辅助场（Z方向）
% 切应力分量Tao_xz（主场 + PML辅助场）
Tao_xz=zeros(nz,nx);    % 主应力场
Tao_xz_1=zeros(nz,nx);  % PML辅助场（X方向）
Tao_xz_3=zeros(nz,nx);  % PML辅助场（Z方向）
%--------------------------------------------------------------------------
%                    1.3 地质模型构建与材料分布
%--------------------------------------------------------------------------

% 1.3.1 材料参数矩阵初始化
vp=zeros(nz,nx);      % 纵波速度分布矩阵
vs=zeros(nz,nx);      % 横波速度分布矩阵
dens=zeros(nz,nx);    % 密度分布矩阵

% 1.3.2 井孔几何位置计算
med_x=fix(nx/2)-fix(nx/4);  % 井轴在X方向的位置
l_cal=ceil(cal/dx)/1;        % 井径对应的网格点数

% 1.3.3 地层结构参数设置
% 井旁侵入带几何参数
Formation_D=1.0;             % 侵入带径向厚度 [m]
Formation_DIter=ceil(Formation_D/dz);  % 侵入带径向网格数
Formation_H=1.0;             % 侵入带轴向高度 [m]
Formation_HIter=ceil(Formation_H/dz);  % 侵入带轴向网格数

% 1.3.4 基本地质模型构建（井孔+围岩）
for count_j=1:1:nx
    if(count_j<med_x-l_cal)      % 左侧地层区域
        vp(:,count_j)=vp2;
        vs(:,count_j)=vs2;
        dens(:,count_j)=po2;
    elseif(count_j>=med_x-l_cal&&count_j<=med_x+l_cal)  % 井孔区域
        vp(:,count_j)=vp1;
        vs(:,count_j)=vs1;
        dens(:,count_j)=po1;
    elseif(count_j>med_x+l_cal)  % 右侧地层区域
        vp(:,count_j)=vp2;
        vs(:,count_j)=vs2;
        dens(:,count_j)=po2;
    end
end

% 1.3.5 井旁侵入带设置（射孔地层）
% 侵入带介质参数
Vpout=2300;  % 侵入带纵波速度 [m/s]
Vsout=1300;  % 侵入带横波速度 [m/s]
Denout=1800; % 侵入带密度 [kg/m^3]
% 侵入带区域赋值
for count_i=nz/2-ceil(Formation_HIter/2):nz/2+ceil(Formation_HIter/2)
    for count_j=med_x+l_cal+1:1:med_x+l_cal+Formation_DIter
        vp(count_i,count_j)=Vpout;
        vs(count_i,count_j)=Vsout;
        dens(count_i,count_j)=Denout;
    end
end

% 1.3.6 地质模型可视化显示
figure(3001);imagesc(vp);title('纵波速度分布');
figure(3002);imagesc(dens);title('密度分布');
figure(3003);imagesc(vs);title('横波速度分布');

%--------------------------------------------------------------------------
%                    1.4 弹性参数与FDTD系数计算
%--------------------------------------------------------------------------

% 1.4.1 Lamé弹性常数计算
% 切变模量计算
miu=zeros(nz,nx);
for count_i=1:1:nz
    for count_j=1:1:nx
        miu(count_i,count_j)=dens(count_i,count_j)*vs(count_i,count_j)^2;
    end
end
% 第一Lamé常数计算
lmd=zeros(nz,nx);
for count_i=1:1:nz
    for count_j=1:1:nx
        lmd(count_i,count_j)=dens(count_i,count_j)*vp(count_i,count_j)^2-2*miu(count_i,count_j);
    end
end

% 1.4.2 FDTD算法更新系数计算
% 系数矩阵初始化
p0=zeros(nz,nx);  % 速度更新系数（密度相关）
p1=zeros(nz,nx);  % 应力更新系数（纵波相关）
p2=zeros(nz,nx);  % 应力更新系数（横波相关）
p3=zeros(nz,nx);  % 应力更新系数（切应力）
% 系数计算（交错网格处理）
for count_i=1:1:nz
    for count_j=2:1:nx-1
        p0(count_i,count_j)=2*dt/(dens(count_i,count_j+1)+dens(count_i,count_j-1));
        p1(count_i,count_j)=dt*(lmd(count_i,count_j)+2*miu(count_i,count_j));
        p2(count_i,count_j)=dt*lmd(count_i,count_j);
        p3(count_i,count_j)=dt*miu(count_i,count_j);
    end
end
% 边界特殊处理
p0(:,1)=dt./dens(:,1);
p0(:,nx)=dt./dens(:,nx);
%==========================================================================
%                    第二章：震源函数与边界条件
%==========================================================================
% 功能说明：设计声波测井正演模拟的震源函数和PML吸收边界条件，
% 初始化相关参数和数据存储结构。
%==========================================================================

%--------------------------------------------------------------------------
%                    2.1 PML吸收边界条件设计
%--------------------------------------------------------------------------

% 2.1.1 PML吸收参数设置
M=5;                    % PML吸收阶数（多项式阶数）
R=1.0e-6;               % 理论反射系数（吸收效果）
da=log(1/R)*1.5*vmax;   % PML吸收系数（最大吸收强度）

% 2.1.2 PML系数矩阵初始化
% Z方向PML系数
kz=0;                   % Z方向归一化坐标
w3=0;                   % Z方向吸收系数
az=zeros(nz,nx);        % Z方向PML系数A
bz=zeros(nz,nx);        % Z方向PML系数B
% X方向PML系数
kx=0;                   % X方向归一化坐标
w1=0;                   % X方向吸收系数
ax=zeros(nz,nx);        % X方向PML系数A
bx=zeros(nz,nx);        % X方向PML系数B

%--------------------------------------------------------------------------
%                    2.2 声波测井震源函数设计
%--------------------------------------------------------------------------

% 2.2.1 Ricker子波时间参数设置
maxt=2000;              % 时间采样点数（总时长）
f=zeros(1,maxt);        % 震源函数数组初始化
kexi=f0^2/0.1512;       % Ricker子波形状参数（频率相关）
ts=1.5/f0;              % 时移参数（延迟时间）

% 2.2.2 Ricker子波函数计算
t=(0:maxt-1).*dt;       % 时间轴构建
for count_i=1:1:maxt
    T=t(count_i)-ts;    % 相对时间
    f(count_i)=2*kexi*(1-2*kexi*T^2)*exp(-kexi*T^2);  % Ricker子波公式
end

%--------------------------------------------------------------------------
%                    2.3 计算辅助变量与数据存储初始化
%--------------------------------------------------------------------------

% 2.3.1 循环计数器初始化
count_i=0;              % 循环计数器i（行索引）
count_j=0;              % 循环计数器j（列索引）

% 2.3.2 临时计算变量初始化
tep=0;                  % 通用临时变量
tep1=0;                 % 临时变量1（应力计算）
tep2=0;                 % 临时变量2（应力计算）
tep3=0;                 % 临时变量3（应力计算）

% 2.3.3 声波测井数据采集系统初始化
N=21;                   % 检波器阵列道数（接收器数量）
data=zeros(num_s,N*maxt); % 全部数据存储矩阵（炮数×道数×时间点）
X=zeros(N,maxt);%单炮数据矩阵（N道×时间采样）

tic
count_F=1;
%==========================================================================
%                    第三章：FDTD主循环算法
%==========================================================================
% 功能说明：实现二维弹性波FDTD时域有限差分算法，采用蛙跳时间步进格式，
% 包括多炮循环、应力-速度场交替更新、PML吸收边界处理。
% 算法特点：交错网格、四阶空间精度、二阶时间精度、分裂式PML边界。
%==========================================================================

%--------------------------------------------------------------------------
%                    3.1 多炮激发循环与场变量初始化
%--------------------------------------------------------------------------

% 3.1.1 炮点位置计算与多炮循环
for count_s=1:1:num_s
    pos_s=nz-3*pml-(count_s-1)*len_RtoR;  % 当前炮点Z轴位置（从下向上移动）
    
    % 3.1.2 波场变量全域归零初始化
    % 说明：每炮激发前清零所有场变量，确保炮间独立性
    for count_i=1:1:nz
        for count_j=1:1:nx
            % 速度场归零（主场+PML分裂辅助场）
            Vx(count_i,count_j)=0;      Vx_1(count_i,count_j)=0;    Vx_3(count_i,count_j)=0;
            Vz(count_i,count_j)=0;      Vz_1(count_i,count_j)=0;    Vz_3(count_i,count_j)=0;
            % 正应力场归零（主场+PML分裂辅助场）
            Tao_xx(count_i,count_j)=0;  Tao_xx_1(count_i,count_j)=0; Tao_xx_3(count_i,count_j)=0;
            Tao_zz(count_i,count_j)=0;  Tao_zz_1(count_i,count_j)=0; Tao_zz_3(count_i,count_j)=0;
            % 切应力场归零（主场+PML分裂辅助场）
            Tao_xz(count_i,count_j)=0;  Tao_xz_1(count_i,count_j)=0; Tao_xz_3(count_i,count_j)=0;
        end
    end
    
%--------------------------------------------------------------------------
%                    3.2 FDTD蛙跳时间步进主循环
%--------------------------------------------------------------------------

% 3.2.1 时间步进主循环
for count_t=1:1:maxt
   
    %==================================================================
    %                3.2.2 应力场更新（蛙跳格式第一步）
    %==================================================================
    
    % A. 震源激发（单极子Ricker子波加载）
    Tao_xx(pos_s,med_x) = Tao_xx(pos_s,med_x) + f(count_t);  % X方向正应力加载
    Tao_zz(pos_s,med_x) = Tao_zz(pos_s,med_x) + f(count_t);  % Z方向正应力加载
        
        % B. 内部区域应力场更新（标准弹性波FDTD）
        % 说明：内部区域不需PML吸收，使用四阶空间精度差分格式
        
        % X方向正应力分量：Tao_xx = (\lambda+2\mu)*\partial Vx/\partial x + \lambda*\partial Vz/\partial z
        Tao_xx(pml+1:nz-pml,pml+1:nx-pml) = Tao_xx(pml+1:nz-pml,pml+1:nx-pml) + ...
            p1(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Vx(pml+1:nz-pml,(pml+1:nx-pml)+1)-Vx(pml+1:nz-pml,(pml+1:nx-pml)-1))/dx + ...
                                           n2*(Vx(pml+1:nz-pml,(pml+1:nx-pml)+2)-Vx(pml+1:nz-pml,(pml+1:nx-pml)-2))/dx) + ...
            p2(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Vz((pml+1:nz-pml)+1,pml+1:nx-pml)-Vz((pml+1:nz-pml)-1,pml+1:nx-pml))/dz + ...
                                           n2*(Vz((pml+1:nz-pml)+2,pml+1:nx-pml)-Vz((pml+1:nz-pml)-2,pml+1:nx-pml))/dz);
        
        % Z方向正应力分量：Tao_zz = \lambda*\partial Vx/\partial x + (\lambda+2\mu)*\partial Vz/\partial z
        Tao_zz(pml+1:nz-pml,pml+1:nx-pml) = Tao_zz(pml+1:nz-pml,pml+1:nx-pml) + ...
            p2(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Vx(pml+1:nz-pml,(pml+1:nx-pml)+1)-Vx(pml+1:nz-pml,(pml+1:nx-pml)-1))/dx + ...
                                           n2*(Vx(pml+1:nz-pml,(pml+1:nx-pml)+2)-Vx(pml+1:nz-pml,(pml+1:nx-pml)-2))/dx) + ...
            p1(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Vz((pml+1:nz-pml)+1,pml+1:nx-pml)-Vz((pml+1:nz-pml)-1,pml+1:nx-pml))/dz + ...
                                           n2*(Vz((pml+1:nz-pml)+2,pml+1:nx-pml)-Vz((pml+1:nz-pml)-2,pml+1:nx-pml))/dz);
        
        % XZ切应力分量：Tao_xz = \mu*(\partial Vx/\partial z + \partial Vz/\partial x)
        Tao_xz(pml+1:nz-pml,pml+1:nx-pml) = Tao_xz(pml+1:nz-pml,pml+1:nx-pml) + ...
            p3(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Vx((pml+1:nz-pml)+1,pml+1:nx-pml)-Vx((pml+1:nz-pml)-1,pml+1:nx-pml))/dz + ...
                                           n2*(Vx((pml+1:nz-pml)+2,pml+1:nx-pml)-Vx((pml+1:nz-pml)-2,pml+1:nx-pml))/dz + ...
                                           n1*(Vz(pml+1:nz-pml,(pml+1:nx-pml)+1)-Vz(pml+1:nz-pml,(pml+1:nx-pml)-1))/dx + ...
                                           n2*(Vz(pml+1:nz-pml,(pml+1:nx-pml)+2)-Vz(pml+1:nz-pml,(pml+1:nx-pml)-2))/dx);

        % C. PML吸收边界区域应力场更新（分裂式PML算法）
        % 说明：PML边界分为9个区域，每个区域的吸收方向和强度不同
        %      分裂式PML算法将每个应力分量分解为X和Z方向的独立分裂项
        
        % C.1 PML吸收系数预计算
        % 上边界（Z方向吸收参数）
        for count_i=1+2:pml
            kz = (pml-count_i)/pml;                 % Z方向归一化坐标(0~1)
            w3 = da*kz^M;                           % Z方向吸收系数
            az(count_i,:) = 1/(1+0.5*w3*dt);        % Z方向PML系数A
            bz(count_i,:) = 1-0.5*w3*dt;            % Z方向PML系数B
        end
        
        % 左边界（X方向吸收参数）
        for count_j=1+2:pml
            kx = (pml-count_j)/pml;                 % X方向归一化坐标(0~1)
            w1 = da*kx^M;                           % X方向吸收系数
            ax(:,count_j) = 1/(1+0.5*w1*dt);        % X方向PML系数A
            bx(:,count_j) = 1-0.5*w1*dt;            % X方向PML系数B
        end
        
        % C.2 PML区域应力场更新（按区域编号顺序处理）
        % PML区域分布示意图：
        %   1---2---3
        %   |   |   |
        %   4---5---6
        %   |   |   |
        %   7---8---9
        % 其中：1,3,7,9为角区（双向吸收）；2,4,6,8为边区（单向吸收）；5为内部区（无吸收）
         
        % 区域1：左上角区域（X和Z方向双向PML吸收）
        % 说明：该区域同时需要X和Z两个方向的吸收，使用分裂式PML算法
    
    % X方向正应力分量更新：Tao_xx
    % X方向分裂项（与Vx的X方向导数相关）
    Tao_xx_1(3:pml,3:pml)=ax(3:pml,3:pml).*(bx(3:pml,3:pml).*Tao_xx_1(3:pml,3:pml)+p1(3:pml,3:pml).*(n1*(Vx(3:pml,(3:pml)+1)-Vx(3:pml,(3:pml)-1))/dx+...
                n2*(Vx(3:pml,(3:pml)+2)-Vx(3:pml,(3:pml)-2))/dx));
    % Z方向分裂项（与Vz的Z方向导数相关）
    Tao_xx_3(3:pml,3:pml)=az(3:pml,3:pml).*(bz(3:pml,3:pml).*Tao_xx_3(3:pml,3:pml)+p2(3:pml,3:pml).*(n1*(Vz((3:pml)+1,3:pml)-Vz((3:pml)-1,3:pml))/dz+...
                n2*(Vz((3:pml)+2,3:pml)-Vz((3:pml)-2,3:pml))/dz));
    % 合成总应力（分裂项相加）
    Tao_xx(3:pml,3:pml)=Tao_xx_1(3:pml,3:pml)+Tao_xx_3(3:pml,3:pml);
    
    % Z方向正应力分量更新：Tao_zz
    % X方向分裂项（与Vx的X方向导数相关）
    Tao_zz_1(3:pml,3:pml)=ax(3:pml,3:pml).*(bx(3:pml,3:pml).*Tao_zz_1(3:pml,3:pml)+p2(3:pml,3:pml).*(n1*(Vx(3:pml,(3:pml)+1)-Vx(3:pml,(3:pml)-1))/dx+...
                n2*(Vx(3:pml,(3:pml)+2)-Vx(3:pml,(3:pml)-2))/dx));
    % Z方向分裂项（与Vz的Z方向导数相关）
    Tao_zz_3(3:pml,3:pml)=az(3:pml,3:pml).*(bz(3:pml,3:pml).*Tao_zz_3(3:pml,3:pml)+p1(3:pml,3:pml).*(n1*(Vz((3:pml)+1,3:pml)-Vz((3:pml)-1,3:pml))/dz+...
                n2*(Vz((3:pml)+2,3:pml)-Vz((3:pml)-2,3:pml))/dz));
    % 合成总应力（分裂项相加）
    Tao_zz(3:pml,3:pml)=Tao_zz_1(3:pml,3:pml)+Tao_zz_3(3:pml,3:pml);
    
    % XZ切应力分量更新：Tao_xz
    % X方向分裂项（与Vz的X方向导数相关）
    Tao_xz_1(3:pml,3:pml)=ax(3:pml,3:pml).*(bx(3:pml,3:pml).*Tao_xz_1(3:pml,3:pml)+p3(3:pml,3:pml).*(n1*(Vz(3:pml,(3:pml)+1)-Vz(3:pml,(3:pml)-1))/dx+...
                n2*(Vz(3:pml,(3:pml)+2)-Vz(3:pml,(3:pml)-2))/dx));
    % Z方向分裂项（与Vx的Z方向导数相关）
    Tao_xz_3(3:pml,3:pml)=az(3:pml,3:pml).*(bz(3:pml,3:pml).*Tao_xz_3(3:pml,3:pml)+p3(3:pml,3:pml).*(n1*(Vx((3:pml)+1,3:pml)-Vx((3:pml)-1,3:pml))/dz+...
                n2*(Vx((3:pml)+2,3:pml)-Vx((3:pml)-2,3:pml))/dz));
    % 合成总应力（分裂项相加）
    Tao_xz(3:pml,3:pml)=Tao_xz_1(3:pml,3:pml)+Tao_xz_3(3:pml,3:pml);
            
    % 区域2：中上区域（仅Z方向单向PML吸收）
    % 说明：该区域仅需要Z方向吸收，X方向不需要吸收
    
    % X方向正应力分量更新：Tao_xx
    % X方向分裂项（无吸收，直接累加）
    Tao_xx_1(3:pml,pml+1:nx-pml)=Tao_xx_1(3:pml,pml+1:nx-pml)+p1(3:pml,pml+1:nx-pml).*(n1*(Vx(3:pml,(pml+1:nx-pml)+1)-Vx(3:pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Vx(3:pml,(pml+1:nx-pml)+2)-Vx(3:pml,(pml+1:nx-pml)-2))/dx);
    % Z方向分裂项（需要Z方向PML吸收）
    Tao_xx_3(3:pml,pml+1:nx-pml)=az(3:pml,pml+1:nx-pml).*(bz(3:pml,pml+1:nx-pml).*Tao_xx_3(3:pml,pml+1:nx-pml)+p2(3:pml,pml+1:nx-pml).*(n1*(Vz((3:pml)+1,pml+1:nx-pml)-Vz((3:pml)-1,pml+1:nx-pml))/dz+...
                n2*(Vz((3:pml)+2,pml+1:nx-pml)-Vz((3:pml)-2,pml+1:nx-pml))/dz));
    % 合成总应力
    Tao_xx(3:pml,pml+1:nx-pml)=Tao_xx_1(3:pml,pml+1:nx-pml)+Tao_xx_3(3:pml,pml+1:nx-pml);
    
    % Z方向正应力分量更新：Tao_zz
    % X方向分裂项（无吸收，直接累加）
    Tao_zz_1(3:pml,pml+1:nx-pml)=Tao_zz_1(3:pml,pml+1:nx-pml)+p2(3:pml,pml+1:nx-pml).*(n1*(Vx(3:pml,(pml+1:nx-pml)+1)-Vx(3:pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Vx(3:pml,(pml+1:nx-pml)+2)-Vx(3:pml,(pml+1:nx-pml)-2))/dx);
    % Z方向分裂项（需要Z方向PML吸收）
    Tao_zz_3(3:pml,pml+1:nx-pml)=az(3:pml,pml+1:nx-pml).*(bz(3:pml,pml+1:nx-pml).*Tao_zz_3(3:pml,pml+1:nx-pml)+p1(3:pml,pml+1:nx-pml).*(n1*(Vz((3:pml)+1,pml+1:nx-pml)-Vz((3:pml)-1,pml+1:nx-pml))/dz+...
                n2*(Vz((3:pml)+2,pml+1:nx-pml)-Vz((3:pml)-2,pml+1:nx-pml))/dz));
    % 合成总应力
    Tao_zz(3:pml,pml+1:nx-pml)=Tao_zz_1(3:pml,pml+1:nx-pml)+Tao_zz_3(3:pml,pml+1:nx-pml);
    
    % XZ切应力分量更新：Tao_xz
    % X方向分裂项（无吸收，直接累加）
    Tao_xz_1(3:pml,pml+1:nx-pml)=Tao_xz_1(3:pml,pml+1:nx-pml)+p3(3:pml,pml+1:nx-pml).*(n1*(Vz(3:pml,(pml+1:nx-pml)+1)-Vz(3:pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Vz(3:pml,(pml+1:nx-pml)+2)-Vz(3:pml,(pml+1:nx-pml)-2))/dx);
    % Z方向分裂项（需要Z方向PML吸收）
    Tao_xz_3(3:pml,pml+1:nx-pml)=az(3:pml,pml+1:nx-pml).*(bz(3:pml,pml+1:nx-pml).*Tao_xz_3(3:pml,pml+1:nx-pml)+p3(3:pml,pml+1:nx-pml).*(n1*(Vx((3:pml)+1,pml+1:nx-pml)-Vx((3:pml)-1,pml+1:nx-pml))/dz+...
                n2*(Vx((3:pml)+2,pml+1:nx-pml)-Vx((3:pml)-2,pml+1:nx-pml))/dz));
    % 合成总应力
    Tao_xz(3:pml,pml+1:nx-pml)=Tao_xz_1(3:pml,pml+1:nx-pml)+Tao_xz_3(3:pml,pml+1:nx-pml);
    
    % 区域3：右上角区域（X和Z方向双向PML吸收）
    % 说明：该区域同时需要X和Z两个方向的吸收，使用分裂式PML算法
    % 右边界X方向吸收参数计算
    for count_j=nx-pml+1:nx-2
        kx=(count_j-(nx-pml+1))/pml;        % X方向归一化坐标(0~1)
        w1=da*kx^M;                         % X方向吸收系数
        ax(:,count_j)=1/(1+0.5*w1*dt);      % X方向PML系数A
        bx(:,count_j)=1-0.5*w1*dt;          % X方向PML系数B
    end
            %Tao_xx
            Tao_xx_1(3:pml,nx-pml+1:nx-2)=ax(3:pml,nx-pml+1:nx-2).*(bx(3:pml,nx-pml+1:nx-2).*Tao_xx_1(3:pml,nx-pml+1:nx-2)+p1(3:pml,nx-pml+1:nx-2).*(n1*(Vx(3:pml,(nx-pml+1:nx-2)+1)-Vx(3:pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vx(3:pml,(nx-pml+1:nx-2)+2)-Vx(3:pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_xx_3(3:pml,nx-pml+1:nx-2)=az(3:pml,nx-pml+1:nx-2).*(bz(3:pml,nx-pml+1:nx-2).*Tao_xx_3(3:pml,nx-pml+1:nx-2)+p2(3:pml,nx-pml+1:nx-2).*(n1*(Vz((3:pml)+1,nx-pml+1:nx-2)-Vz((3:pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vz((3:pml)+2,nx-pml+1:nx-2)-Vz((3:pml)-2,nx-pml+1:nx-2))/dz));
            Tao_xx(3:pml,nx-pml+1:nx-2)=Tao_xx_1(3:pml,nx-pml+1:nx-2)+Tao_xx_3(3:pml,nx-pml+1:nx-2);
            %Tao_zz
            Tao_zz_1(3:pml,nx-pml+1:nx-2)=ax(3:pml,nx-pml+1:nx-2).*(bx(3:pml,nx-pml+1:nx-2).*Tao_zz_1(3:pml,nx-pml+1:nx-2)+p2(3:pml,nx-pml+1:nx-2).*(n1*(Vx(3:pml,(nx-pml+1:nx-2)+1)-Vx(3:pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vx(3:pml,(nx-pml+1:nx-2)+2)-Vx(3:pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_zz_3(3:pml,nx-pml+1:nx-2)=az(3:pml,nx-pml+1:nx-2).*(bz(3:pml,nx-pml+1:nx-2).*Tao_zz_3(3:pml,nx-pml+1:nx-2)+p1(3:pml,nx-pml+1:nx-2).*(n1*(Vz((3:pml)+1,nx-pml+1:nx-2)-Vz((3:pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vz((3:pml)+2,nx-pml+1:nx-2)-Vz((3:pml)-2,nx-pml+1:nx-2))/dz));
            Tao_zz(3:pml,nx-pml+1:nx-2)=Tao_zz_1(3:pml,nx-pml+1:nx-2)+Tao_zz_3(3:pml,nx-pml+1:nx-2);
            %Tao_xz
            Tao_xz_1(3:pml,nx-pml+1:nx-2)=ax(3:pml,nx-pml+1:nx-2).*(bx(3:pml,nx-pml+1:nx-2).*Tao_xz_1(3:pml,nx-pml+1:nx-2)+p3(3:pml,nx-pml+1:nx-2).*(n1*(Vz(3:pml,(nx-pml+1:nx-2)+1)-Vz(3:pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vz(3:pml,(nx-pml+1:nx-2)+2)-Vz(3:pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_xz_3(3:pml,nx-pml+1:nx-2)=az(3:pml,nx-pml+1:nx-2).*(bz(3:pml,nx-pml+1:nx-2).*Tao_xz_3(3:pml,nx-pml+1:nx-2)+p3(3:pml,nx-pml+1:nx-2).*(n1*(Vx((3:pml)+1,nx-pml+1:nx-2)-Vx((3:pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vx((3:pml)+2,nx-pml+1:nx-2)-Vx((3:pml)-2,nx-pml+1:nx-2))/dz));
            Tao_xz(3:pml,nx-pml+1:nx-2)=Tao_xz_1(3:pml,nx-pml+1:nx-2)+Tao_xz_3(3:pml,nx-pml+1:nx-2);       
   
    % 左边界区域（仅X方向吸收：w1!=0,w3=0）
        for count_j=1+2:pml
            kx=(pml-count_j)/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            Tao_xx_1(pml+1:nz-pml,3:pml)=ax(pml+1:nz-pml,3:pml).*(bx(pml+1:nz-pml,3:pml).*Tao_xx_1(pml+1:nz-pml,3:pml)+p1(pml+1:nz-pml,3:pml).*(n1*(Vx(pml+1:nz-pml,(3:pml)+1)-Vx(pml+1:nz-pml,(3:pml)-1))/dx+...
                n2*(Vx(pml+1:nz-pml,(3:pml)+2)-Vx(pml+1:nz-pml,(3:pml)-2))/dx));
            Tao_xx_3(pml+1:nz-pml,3:pml)=Tao_xx_3(pml+1:nz-pml,3:pml)+p2(pml+1:nz-pml,3:pml).*(n1*(Vz((pml+1:nz-pml)+1,3:pml)-Vz((pml+1:nz-pml)-1,3:pml))/dz+...
                n2*(Vz((pml+1:nz-pml)+2,3:pml)-Vz((pml+1:nz-pml)-2,3:pml))/dz);
            Tao_xx(pml+1:nz-pml,3:pml)=Tao_xx_1(pml+1:nz-pml,3:pml)+Tao_xx_3(pml+1:nz-pml,3:pml);
            Tao_zz_1(pml+1:nz-pml,3:pml)=ax(pml+1:nz-pml,3:pml).*(bx(pml+1:nz-pml,3:pml).*Tao_zz_1(pml+1:nz-pml,3:pml)+p2(pml+1:nz-pml,3:pml).*(n1*(Vx(pml+1:nz-pml,(3:pml)+1)-Vx(pml+1:nz-pml,(3:pml)-1))/dx+...
                n2*(Vx(pml+1:nz-pml,(3:pml)+2)-Vx(pml+1:nz-pml,(3:pml)-2))/dx));
            Tao_zz_3(pml+1:nz-pml,3:pml)=Tao_zz_3(pml+1:nz-pml,3:pml)+p1(pml+1:nz-pml,3:pml).*(n1*(Vz((pml+1:nz-pml)+1,3:pml)-Vz((pml+1:nz-pml)-1,3:pml))/dz+...
                n2*(Vz((pml+1:nz-pml)+2,3:pml)-Vz((pml+1:nz-pml)-2,3:pml))/dz);
            Tao_zz(pml+1:nz-pml,3:pml)=Tao_zz_1(pml+1:nz-pml,3:pml)+Tao_zz_3(pml+1:nz-pml,3:pml);
            Tao_xz_1(pml+1:nz-pml,3:pml)=ax(pml+1:nz-pml,3:pml).*(bx(pml+1:nz-pml,3:pml).*Tao_xz_1(pml+1:nz-pml,3:pml)+p3(pml+1:nz-pml,3:pml).*(n1*(Vz(pml+1:nz-pml,(3:pml)+1)-Vz(pml+1:nz-pml,(3:pml)-1))/dx+...
                n2*(Vz(pml+1:nz-pml,(3:pml)+2)-Vz(pml+1:nz-pml,(3:pml)-2))/dx));
            Tao_xz_3(pml+1:nz-pml,3:pml)=Tao_xz_3(pml+1:nz-pml,3:pml)+p3(pml+1:nz-pml,3:pml).*(n1*(Vx((pml+1:nz-pml)+1,3:pml)-Vx((pml+1:nz-pml)-1,3:pml))/dz+...
                n2*(Vx((pml+1:nz-pml)+2,3:pml)-Vx((pml+1:nz-pml)-2,3:pml))/dz);
            Tao_xz(pml+1:nz-pml,3:pml)=Tao_xz_1(pml+1:nz-pml,3:pml)+Tao_xz_3(pml+1:nz-pml,3:pml);       
    
    % 右边界区域（仅X方向吸收：w1!=0,w3=0）
        for count_j=nx-pml+1:nx-2
            kx=(count_j-(nx-pml+1))/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            Tao_xx_1(pml+1:nz-pml,nx-pml+1:nx-2)=ax(pml+1:nz-pml,nx-pml+1:nx-2).*(bx(pml+1:nz-pml,nx-pml+1:nx-2).*Tao_xx_1(pml+1:nz-pml,nx-pml+1:nx-2)+p1(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vx(pml+1:nz-pml,(nx-pml+1:nx-2)+1)-Vx(pml+1:nz-pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vx(pml+1:nz-pml,(nx-pml+1:nx-2)+2)-Vx(pml+1:nz-pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_xx_3(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_xx_3(pml+1:nz-pml,nx-pml+1:nx-2)+p2(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vz((pml+1:nz-pml)+1,nx-pml+1:nx-2)-Vz((pml+1:nz-pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vz((pml+1:nz-pml)+2,nx-pml+1:nx-2)-Vz((pml+1:nz-pml)-2,nx-pml+1:nx-2))/dz);
            Tao_xx(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_xx_1(pml+1:nz-pml,nx-pml+1:nx-2)+Tao_xx_3(pml+1:nz-pml,nx-pml+1:nx-2);
            Tao_zz_1(pml+1:nz-pml,nx-pml+1:nx-2)=ax(pml+1:nz-pml,nx-pml+1:nx-2).*(bx(pml+1:nz-pml,nx-pml+1:nx-2).*Tao_zz_1(pml+1:nz-pml,nx-pml+1:nx-2)+p2(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vx(pml+1:nz-pml,(nx-pml+1:nx-2)+1)-Vx(pml+1:nz-pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vx(pml+1:nz-pml,(nx-pml+1:nx-2)+2)-Vx(pml+1:nz-pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_zz_3(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_zz_3(pml+1:nz-pml,nx-pml+1:nx-2)+p1(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vz((pml+1:nz-pml)+1,nx-pml+1:nx-2)-Vz((pml+1:nz-pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vz((pml+1:nz-pml)+2,nx-pml+1:nx-2)-Vz((pml+1:nz-pml)-2,nx-pml+1:nx-2))/dz);
            Tao_zz(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_zz_1(pml+1:nz-pml,nx-pml+1:nx-2)+Tao_zz_3(pml+1:nz-pml,nx-pml+1:nx-2);
            Tao_xz_1(pml+1:nz-pml,nx-pml+1:nx-2)=ax(pml+1:nz-pml,nx-pml+1:nx-2).*(bx(pml+1:nz-pml,nx-pml+1:nx-2).*Tao_xz_1(pml+1:nz-pml,nx-pml+1:nx-2)+p3(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vz(pml+1:nz-pml,(nx-pml+1:nx-2)+1)-Vz(pml+1:nz-pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Vz(pml+1:nz-pml,(nx-pml+1:nx-2)+2)-Vz(pml+1:nz-pml,(nx-pml+1:nx-2)-2))/dx));
            Tao_xz_3(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_xz_3(pml+1:nz-pml,nx-pml+1:nx-2)+p3(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Vx((pml+1:nz-pml)+1,nx-pml+1:nx-2)-Vx((pml+1:nz-pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Vx((pml+1:nz-pml)+2,nx-pml+1:nx-2)-Vx((pml+1:nz-pml)-2,nx-pml+1:nx-2))/dz);
            Tao_xz(pml+1:nz-pml,nx-pml+1:nx-2)=Tao_xz_1(pml+1:nz-pml,nx-pml+1:nx-2)+Tao_xz_3(pml+1:nz-pml,nx-pml+1:nx-2);    
    
    % 下边界PML系数计算（Z方向吸收）
    for count_i=nz-pml+1:nz-2
        kz=(count_i-(nz-pml+1))/pml;
        w3=da*kz^M;
        az(count_i,:)=1/(1+0.5*w3*dt);
        bz(count_i,:)=1-0.5*w3*dt;
    end
    
    % 区域7：左下角区域（X和Z方向双向PML吸收）
    % 说明：该区域同时需要X和Z两个方向的吸收，使用分裂式PML算法
    % 左边界X方向吸收参数计算（与区域1相同）
    for count_j=1+2:pml
        kx=(pml-count_j)/pml;               % X方向归一化坐标(0~1)
        w1=da*kx^M;                         % X方向吸收系数
        ax(:,count_j)=1/(1+0.5*w1*dt);      % X方向PML系数A
        bx(:,count_j)=1-0.5*w1*dt;          % X方向PML系数B
    end
    
    % X方向正应力分量更新：Tao_xx
    % X方向分裂项（与Vx的X方向导数相关）
    Tao_xx_1(nz-pml+1:nz-2,3:pml)=ax(nz-pml+1:nz-2,3:pml).*(bx(nz-pml+1:nz-2,3:pml).*Tao_xx_1(nz-pml+1:nz-2,3:pml)+p1(nz-pml+1:nz-2,3:pml).*(n1*(Vx(nz-pml+1:nz-2,(3:pml)+1)-Vx(nz-pml+1:nz-2,(3:pml)-1))/dx+...
                n2*(Vx(nz-pml+1:nz-2,(3:pml)+2)-Vx(nz-pml+1:nz-2,(3:pml)-2))/dx));
    % Z方向分裂项（与Vz的Z方向导数相关）
    Tao_xx_3(nz-pml+1:nz-2,3:pml)=az(nz-pml+1:nz-2,3:pml).*(bz(nz-pml+1:nz-2,3:pml).*Tao_xx_3(nz-pml+1:nz-2,3:pml)+p2(nz-pml+1:nz-2,3:pml).*(n1*(Vz((nz-pml+1:nz-2)+1,3:pml)-Vz((nz-pml+1:nz-2)-1,3:pml))/dz+...
                n2*(Vz((nz-pml+1:nz-2)+2,3:pml)-Vz((nz-pml+1:nz-2)-2,3:pml))/dz));
    % 合成总应力（分裂项相加）
    Tao_xx(nz-pml+1:nz-2,3:pml)=Tao_xx_1(nz-pml+1:nz-2,3:pml)+Tao_xx_3(nz-pml+1:nz-2,3:pml);
    
    % Z方向正应力分量更新：Tao_zz
    % X方向分裂项（与Vx的X方向导数相关）
    Tao_zz_1(nz-pml+1:nz-2,3:pml)=ax(nz-pml+1:nz-2,3:pml).*(bx(nz-pml+1:nz-2,3:pml).*Tao_zz_1(nz-pml+1:nz-2,3:pml)+p2(nz-pml+1:nz-2,3:pml).*(n1*(Vx(nz-pml+1:nz-2,(3:pml)+1)-Vx(nz-pml+1:nz-2,(3:pml)-1))/dx+...
                n2*(Vx(nz-pml+1:nz-2,(3:pml)+2)-Vx(nz-pml+1:nz-2,(3:pml)-2))/dx));
    % Z方向分裂项（与Vz的Z方向导数相关）
    Tao_zz_3(nz-pml+1:nz-2,3:pml)=az(nz-pml+1:nz-2,3:pml).*(bz(nz-pml+1:nz-2,3:pml).*Tao_zz_3(nz-pml+1:nz-2,3:pml)+p1(nz-pml+1:nz-2,3:pml).*(n1*(Vz((nz-pml+1:nz-2)+1,3:pml)-Vz((nz-pml+1:nz-2)-1,3:pml))/dz+...
                n2*(Vz((nz-pml+1:nz-2)+2,3:pml)-Vz((nz-pml+1:nz-2)-2,3:pml))/dz));
    % 合成总应力（分裂项相加）
    Tao_zz(nz-pml+1:nz-2,3:pml)=Tao_zz_1(nz-pml+1:nz-2,3:pml)+Tao_zz_3(nz-pml+1:nz-2,3:pml);
    
    % XZ切应力分量更新：Tao_xz
    % X方向分裂项（与Vz的X方向导数相关）
    Tao_xz_1(nz-pml+1:nz-2,3:pml)=ax(nz-pml+1:nz-2,3:pml).*(bx(nz-pml+1:nz-2,3:pml).*Tao_xz_1(nz-pml+1:nz-2,3:pml)+p3(nz-pml+1:nz-2,3:pml).*(n1*(Vz(nz-pml+1:nz-2,(3:pml)+1)-Vz(nz-pml+1:nz-2,(3:pml)-1))/dx+...
                n2*(Vz(nz-pml+1:nz-2,(3:pml)+2)-Vz(nz-pml+1:nz-2,(3:pml)-2))/dx));
    % Z方向分裂项（与Vx的Z方向导数相关）
    Tao_xz_3(nz-pml+1:nz-2,3:pml)=az(nz-pml+1:nz-2,3:pml).*(bz(nz-pml+1:nz-2,3:pml).*Tao_xz_3(nz-pml+1:nz-2,3:pml)+p3(nz-pml+1:nz-2,3:pml).*(n1*(Vx((nz-pml+1:nz-2)+1,3:pml)-Vx((nz-pml+1:nz-2)-1,3:pml))/dz+...
                n2*(Vx((nz-pml+1:nz-2)+2,3:pml)-Vx((nz-pml+1:nz-2)-2,3:pml))/dz));
    % 合成总应力（分裂项相加）
    Tao_xz(nz-pml+1:nz-2,3:pml)=Tao_xz_1(nz-pml+1:nz-2,3:pml)+Tao_xz_3(nz-pml+1:nz-2,3:pml);
    
    % 区域8：中下区域（仅Z方向单向PML吸收）
    % 说明：该区域仅需要Z方向吸收，X方向不需要吸收
    
    % X方向正应力分量更新：Tao_xx
    % X方向分裂项（无吸收，直接累加）
    Tao_xx_1(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_xx_1(nz-pml+1:nz-2,pml+1:nx-pml)+p1(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vx(nz-pml+1:nz-2,(pml+1:nx-pml)+1)-Vx(nz-pml+1:nz-2,(pml+1:nx-pml)-1))/dx+...
                n2*(Vx(nz-pml+1:nz-2,(pml+1:nx-pml)+2)-Vx(nz-pml+1:nz-2,(pml+1:nx-pml)-2))/dx);
    % Z方向分裂项（需要Z方向PML吸收）
    Tao_xx_3(nz-pml+1:nz-2,pml+1:nx-pml)=az(nz-pml+1:nz-2,pml+1:nx-pml).*(bz(nz-pml+1:nz-2,pml+1:nx-pml).*Tao_xx_3(nz-pml+1:nz-2,pml+1:nx-pml)+p2(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vz((nz-pml+1:nz-2)+1,pml+1:nx-pml)-Vz((nz-pml+1:nz-2)-1,pml+1:nx-pml))/dz+...
                n2*(Vz((nz-pml+1:nz-2)+2,pml+1:nx-pml)-Vz((nz-pml+1:nz-2)-2,pml+1:nx-pml))/dz));
    % 合成总应力
    Tao_xx(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_xx_1(nz-pml+1:nz-2,pml+1:nx-pml)+Tao_xx_3(nz-pml+1:nz-2,pml+1:nx-pml);
    
    % Z方向正应力分量更新：Tao_zz
    % X方向分裂项（无吸收，直接累加）
    Tao_zz_1(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_zz_1(nz-pml+1:nz-2,pml+1:nx-pml)+p2(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vx(nz-pml+1:nz-2,(pml+1:nx-pml)+1)-Vx(nz-pml+1:nz-2,(pml+1:nx-pml)-1))/dx+...
                n2*(Vx(nz-pml+1:nz-2,(pml+1:nx-pml)+2)-Vx(nz-pml+1:nz-2,(pml+1:nx-pml)-2))/dx);
    % Z方向分裂项（需要Z方向PML吸收）
    Tao_zz_3(nz-pml+1:nz-2,pml+1:nx-pml)=az(nz-pml+1:nz-2,pml+1:nx-pml).*(bz(nz-pml+1:nz-2,pml+1:nx-pml).*Tao_zz_3(nz-pml+1:nz-2,pml+1:nx-pml)+p1(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vz((nz-pml+1:nz-2)+1,pml+1:nx-pml)-Vz((nz-pml+1:nz-2)-1,pml+1:nx-pml))/dz+...
                n2*(Vz((nz-pml+1:nz-2)+2,pml+1:nx-pml)-Vz((nz-pml+1:nz-2)-2,pml+1:nx-pml))/dz));
    % 合成总应力
    Tao_zz(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_zz_1(nz-pml+1:nz-2,pml+1:nx-pml)+Tao_zz_3(nz-pml+1:nz-2,pml+1:nx-pml);
    
    % XZ切应力分量更新：Tao_xz
    % X方向分裂项（无吸收，直接累加）
    Tao_xz_1(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_xz_1(nz-pml+1:nz-2,pml+1:nx-pml)+p3(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vz(nz-pml+1:nz-2,(pml+1:nx-pml)+1)-Vz(nz-pml+1:nz-2,(pml+1:nx-pml)-1))/dx+...
                n2*(Vz(nz-pml+1:nz-2,(pml+1:nx-pml)+2)-Vz(nz-pml+1:nz-2,(pml+1:nx-pml)-2))/dx);
    % Z方向分裂项（需要Z方向PML吸收）
    Tao_xz_3(nz-pml+1:nz-2,pml+1:nx-pml)=az(nz-pml+1:nz-2,pml+1:nx-pml).*(bz(nz-pml+1:nz-2,pml+1:nx-pml).*Tao_xz_3(nz-pml+1:nz-2,pml+1:nx-pml)+p3(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Vx((nz-pml+1:nz-2)+1,pml+1:nx-pml)-Vx((nz-pml+1:nz-2)-1,pml+1:nx-pml))/dz+...
                n2*(Vx((nz-pml+1:nz-2)+2,pml+1:nx-pml)-Vx((nz-pml+1:nz-2)-2,pml+1:nx-pml))/dz));
    % 合成总应力
    Tao_xz(nz-pml+1:nz-2,pml+1:nx-pml)=Tao_xz_1(nz-pml+1:nz-2,pml+1:nx-pml)+Tao_xz_3(nz-pml+1:nz-2,pml+1:nx-pml);     

    %---------------------------------更新速度参数--------------------------
    %计算内部区域速度
    %Vx
    Vx(pml+1:nz-pml,pml+1:nx-pml)=Vx(pml+1:nz-pml,pml+1:nx-pml)+p0(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Tao_xx(pml+1:nz-pml,(pml+1:nx-pml)+1)-Tao_xx(pml+1:nz-pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xx(pml+1:nz-pml,(pml+1:nx-pml)+2)-Tao_xx(pml+1:nz-pml,(pml+1:nx-pml)-2))/dx+n1*(Tao_xz((pml+1:nz-pml)+1,pml+1:nx-pml)-Tao_xz((pml+1:nz-pml)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_xz((pml+1:nz-pml)+2,pml+1:nx-pml)-Tao_xz((pml+1:nz-pml)-2,pml+1:nx-pml))/dz);
    %Vz        
    Vz(pml+1:nz-pml,pml+1:nx-pml)=Vz(pml+1:nz-pml,pml+1:nx-pml)+p0(pml+1:nz-pml,pml+1:nx-pml).*(n1*(Tao_xz(pml+1:nz-pml,(pml+1:nx-pml)+1)-Tao_xz(pml+1:nz-pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xz(pml+1:nz-pml,(pml+1:nx-pml)+2)-Tao_xz(pml+1:nz-pml,(pml+1:nx-pml)-2))/dx+n1*(Tao_zz((pml+1:nz-pml)+1,pml+1:nx-pml)-Tao_zz((pml+1:nz-pml)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_zz((pml+1:nz-pml)+2,pml+1:nx-pml)-Tao_zz((pml+1:nz-pml)-2,pml+1:nx-pml))/dz);
 
    % 3.2.3 速度场更新（第二步）
    % 上边界PML系数计算
    for count_i=1+2:pml
        kz=(pml-count_i)/pml;
        w3=da*kz^M;
        az(count_i,:)=1/(1+0.5*w3*dt);
        bz(count_i,:)=1-0.5*w3*dt;
    end
        % 左上角区域（双向PML吸收）
        for count_j=1+2:pml
            kx=(pml-count_j)/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            % Vx速度分量更新
            Vx_1(3:pml,3:pml)=ax(3:pml,3:pml).*(bx(3:pml,3:pml).*Vx_1(3:pml,3:pml)+p0(3:pml,3:pml).*(n1*(Tao_xx(3:pml,(3:pml)+1)-Tao_xx(3:pml,(3:pml)-1))/dx+...
                n2*(Tao_xx(3:pml,(3:pml)+2)-Tao_xx(3:pml,(3:pml)-2))/dx));
            Vx_3(3:pml,3:pml)=az(3:pml,3:pml).*(bz(3:pml,3:pml).*Vx_3(3:pml,3:pml)+p0(3:pml,3:pml).*(n1*(Tao_xz((3:pml)+1,3:pml)-Tao_xz((3:pml)-1,3:pml))/dz+...
                n2*(Tao_xz((3:pml)+2,3:pml)-Tao_xz((3:pml)-2,3:pml))/dz));
            Vx(3:pml,3:pml)=Vx_1(3:pml,3:pml)+Vx_3(3:pml,3:pml);
            % Vz速度分量更新
            Vz_1(3:pml,3:pml)=ax(3:pml,3:pml).*(bx(3:pml,3:pml).*Vz_1(3:pml,3:pml)+p0(3:pml,3:pml).*(n1*(Tao_xz(3:pml,(3:pml)+1)-Tao_xz(3:pml,(3:pml)-1))/dx+...
                n2*(Tao_xz(3:pml,(3:pml)+2)-Tao_xz(3:pml,(3:pml)-2))/dx));
            Vz_3(3:pml,3:pml)=az(3:pml,3:pml).*(bz(3:pml,3:pml).*Vz_3(3:pml,3:pml)+p0(3:pml,3:pml).*(n1*(Tao_zz((3:pml)+1,3:pml)-Tao_zz((3:pml)-1,3:pml))/dz+...
                n2*(Tao_zz((3:pml)+2,3:pml)-Tao_zz((3:pml)-2,3:pml))/dz));
            Vz(3:pml,3:pml)=Vz_1(3:pml,3:pml)+Vz_3(3:pml,3:pml);
        % 中上区域（仅Z方向PML吸收）
            % Vx速度分量更新
            Vx_1(3:pml,pml+1:nx-pml)=Vx_1(3:pml,pml+1:nx-pml)+p0(3:pml,pml+1:nx-pml).*(n1*(Tao_xx(3:pml,(pml+1:nx-pml)+1)-Tao_xx(3:pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xx(3:pml,(pml+1:nx-pml)+2)-Tao_xx(3:pml,(pml+1:nx-pml)-2))/dx);
            Vx_3(3:pml,pml+1:nx-pml)=az(3:pml,pml+1:nx-pml).*(bz(3:pml,pml+1:nx-pml).*Vx_3(3:pml,pml+1:nx-pml)+p0(3:pml,pml+1:nx-pml).*(n1*(Tao_xz((3:pml)+1,pml+1:nx-pml)-Tao_xz((3:pml)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_xz((3:pml)+2,pml+1:nx-pml)-Tao_xz((3:pml)-2,pml+1:nx-pml))/dz));
            Vx(3:pml,pml+1:nx-pml)=Vx_1(3:pml,pml+1:nx-pml)+Vx_3(3:pml,pml+1:nx-pml);
            % Vz速度分量更新
            Vz_1(3:pml,pml+1:nx-pml)=Vz_1(3:pml,pml+1:nx-pml)+p0(3:pml,pml+1:nx-pml).*(n1*(Tao_xz(3:pml,(pml+1:nx-pml)+1)-Tao_xz(3:pml,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xz(3:pml,(pml+1:nx-pml)+2)-Tao_xz(3:pml,(pml+1:nx-pml)-2))/dx);
            Vz_3(3:pml,pml+1:nx-pml)=az(3:pml,pml+1:nx-pml).*(bz(3:pml,pml+1:nx-pml).*Vz_3(3:pml,pml+1:nx-pml)+p0(3:pml,pml+1:nx-pml).*(n1*(Tao_zz((3:pml)+1,pml+1:nx-pml)-Tao_zz((3:pml)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_zz((3:pml)+2,pml+1:nx-pml)-Tao_zz((3:pml)-2,pml+1:nx-pml))/dz));
            Vz(3:pml,pml+1:nx-pml)=Vz_1(3:pml,pml+1:nx-pml)+Vz_3(3:pml,pml+1:nx-pml);
        % 右上角区域（双向PML吸收）
        for count_j=nx-pml+1:nx-2
            kx=(count_j-(nx-pml+1))/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            % Vx速度分量更新
            Vx_1(3:pml,nx-pml+1:nx-2)=ax(3:pml,nx-pml+1:nx-2).*(bx(3:pml,nx-pml+1:nx-2).*Vx_1(3:pml,nx-pml+1:nx-2)+p0(3:pml,nx-pml+1:nx-2).*(n1*(Tao_xx(3:pml,(nx-pml+1:nx-2)+1)-Tao_xx(3:pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xx(3:pml,(nx-pml+1:nx-2)+2)-Tao_xx(3:pml,(nx-pml+1:nx-2)-2))/dx));
            Vx_3(3:pml,nx-pml+1:nx-2)=az(3:pml,nx-pml+1:nx-2).*(bz(3:pml,nx-pml+1:nx-2).*Vx_3(3:pml,nx-pml+1:nx-2)+p0(3:pml,nx-pml+1:nx-2).*(n1*(Tao_xz((3:pml)+1,nx-pml+1:nx-2)-Tao_xz((3:pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_xz((3:pml)+2,nx-pml+1:nx-2)-Tao_xz((3:pml)-2,nx-pml+1:nx-2))/dz));
            Vx(3:pml,nx-pml+1:nx-2)=Vx_1(3:pml,nx-pml+1:nx-2)+Vx_3(3:pml,nx-pml+1:nx-2);
            % Vz速度分量更新
            Vz_1(3:pml,nx-pml+1:nx-2)=ax(3:pml,nx-pml+1:nx-2).*(bx(3:pml,nx-pml+1:nx-2).*Vz_1(3:pml,nx-pml+1:nx-2)+p0(3:pml,nx-pml+1:nx-2).*(n1*(Tao_xz(3:pml,(nx-pml+1:nx-2)+1)-Tao_xz(3:pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xz(3:pml,(nx-pml+1:nx-2)+2)-Tao_xz(3:pml,(nx-pml+1:nx-2)-2))/dx));
            Vz_3(3:pml,nx-pml+1:nx-2)=az(3:pml,nx-pml+1:nx-2).*(bz(3:pml,nx-pml+1:nx-2).*Vz_3(3:pml,nx-pml+1:nx-2)+p0(3:pml,nx-pml+1:nx-2).*(n1*(Tao_zz((3:pml)+1,nx-pml+1:nx-2)-Tao_zz((3:pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_zz((3:pml)+2,nx-pml+1:nx-2)-Tao_zz((3:pml)-2,nx-pml+1:nx-2))/dz));
            Vz(3:pml,nx-pml+1:nx-2)=Vz_1(3:pml,nx-pml+1:nx-2)+Vz_3(3:pml,nx-pml+1:nx-2);
   
    % 左右边界速度场更新（仅X方向PML吸收）
        % 左边界区域
        for count_j=1+2:pml
            kx=(pml-count_j)/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            % Vx速度分量更新
            Vx_1(pml+1:nz-pml,3:pml)=ax(pml+1:nz-pml,3:pml).*(bx(pml+1:nz-pml,3:pml).*Vx_1(pml+1:nz-pml,3:pml)+p0(pml+1:nz-pml,3:pml).*(n1*(Tao_xx(pml+1:nz-pml,(3:pml)+1)-Tao_xx(pml+1:nz-pml,(3:pml)-1))/dx+...
                n2*(Tao_xx(pml+1:nz-pml,(3:pml)+2)-Tao_xx(pml+1:nz-pml,(3:pml)-2))/dx));
            Vx_3(pml+1:nz-pml,3:pml)=Vx_3(pml+1:nz-pml,3:pml)+p0(pml+1:nz-pml,3:pml).*(n1*(Tao_xz((pml+1:nz-pml)+1,3:pml)-Tao_xz((pml+1:nz-pml)-1,3:pml))/dz+...
                n2*(Tao_xz((pml+1:nz-pml)+2,3:pml)-Tao_xz((pml+1:nz-pml)-2,3:pml))/dz);
            Vx(pml+1:nz-pml,3:pml)=Vx_1(pml+1:nz-pml,3:pml)+Vx_3(pml+1:nz-pml,3:pml);
            % Vz速度分量更新
            Vz_1(pml+1:nz-pml,3:pml)=ax(pml+1:nz-pml,3:pml).*(bx(pml+1:nz-pml,3:pml).*Vz_1(pml+1:nz-pml,3:pml)+p0(pml+1:nz-pml,3:pml).*(n1*(Tao_xz(pml+1:nz-pml,(3:pml)+1)-Tao_xz(pml+1:nz-pml,(3:pml)-1))/dx+...
                n2*(Tao_xz(pml+1:nz-pml,(3:pml)+2)-Tao_xz(pml+1:nz-pml,(3:pml)-2))/dx));
            Vz_3(pml+1:nz-pml,3:pml)=Vz_3(pml+1:nz-pml,3:pml)+p0(pml+1:nz-pml,3:pml).*(n1*(Tao_zz((pml+1:nz-pml)+1,3:pml)-Tao_zz((pml+1:nz-pml)-1,3:pml))/dz+...
                n2*(Tao_zz((pml+1:nz-pml)+2,3:pml)-Tao_zz((pml+1:nz-pml)-2,3:pml))/dz);
            Vz(pml+1:nz-pml,3:pml)=Vz_1(pml+1:nz-pml,3:pml)+Vz_3(pml+1:nz-pml,3:pml);
        % 右边界区域
        for count_j=nx-pml+1:nx-2
            kx=(count_j-(nx-pml+1))/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            % Vx速度分量更新
            Vx_1(pml+1:nz-pml,nx-pml+1:nx-2)=ax(pml+1:nz-pml,nx-pml+1:nx-2).*(bx(pml+1:nz-pml,nx-pml+1:nx-2).*Vx_1(pml+1:nz-pml,nx-pml+1:nx-2)+p0(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Tao_xx(pml+1:nz-pml,(nx-pml+1:nx-2)+1)-Tao_xx(pml+1:nz-pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xx(pml+1:nz-pml,(nx-pml+1:nx-2)+2)-Tao_xx(pml+1:nz-pml,(nx-pml+1:nx-2)-2))/dx));
            Vx_3(pml+1:nz-pml,nx-pml+1:nx-2)=Vx_3(pml+1:nz-pml,nx-pml+1:nx-2)+p0(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Tao_xz((pml+1:nz-pml)+1,nx-pml+1:nx-2)-Tao_xz((pml+1:nz-pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_xz((pml+1:nz-pml)+2,nx-pml+1:nx-2)-Tao_xz((pml+1:nz-pml)-2,nx-pml+1:nx-2))/dz);
            Vx(pml+1:nz-pml,nx-pml+1:nx-2)=Vx_1(pml+1:nz-pml,nx-pml+1:nx-2)+Vx_3(pml+1:nz-pml,nx-pml+1:nx-2);
            % Vz速度分量更新
            Vz_1(pml+1:nz-pml,nx-pml+1:nx-2)=ax(pml+1:nz-pml,nx-pml+1:nx-2).*(bx(pml+1:nz-pml,nx-pml+1:nx-2).*Vz_1(pml+1:nz-pml,nx-pml+1:nx-2)+p0(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Tao_xz(pml+1:nz-pml,(nx-pml+1:nx-2)+1)-Tao_xz(pml+1:nz-pml,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xz(pml+1:nz-pml,(nx-pml+1:nx-2)+2)-Tao_xz(pml+1:nz-pml,(nx-pml+1:nx-2)-2))/dx));
            Vz_3(pml+1:nz-pml,nx-pml+1:nx-2)=Vz_3(pml+1:nz-pml,nx-pml+1:nx-2)+p0(pml+1:nz-pml,nx-pml+1:nx-2).*(n1*(Tao_zz((pml+1:nz-pml)+1,nx-pml+1:nx-2)-Tao_zz((pml+1:nz-pml)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_zz((pml+1:nz-pml)+2,nx-pml+1:nx-2)-Tao_zz((pml+1:nz-pml)-2,nx-pml+1:nx-2))/dz);
            Vz(pml+1:nz-pml,nx-pml+1:nx-2)=Vz_1(pml+1:nz-pml,nx-pml+1:nx-2)+Vz_3(pml+1:nz-pml,nx-pml+1:nx-2);
       
    % 下边界速度场更新（仅Z方向PML吸收）
    for count_i=nz-pml+1:nz-2
        kz=(count_i-(nz-pml+1))/pml;
        w3=da*kz^M;
        az(count_i,:)=1/(1+0.5*w3*dt);
        bz(count_i,:)=1-0.5*w3*dt;
    end
        % 左下角区域（双向PML吸收）
        for count_j=1+2:pml
            kx=(pml-count_j)/pml;
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            %Vx
            Vx_1(nz-pml+1:nz-2,3:pml)=ax(nz-pml+1:nz-2,3:pml).*(bx(nz-pml+1:nz-2,3:pml).*Vx_1(nz-pml+1:nz-2,3:pml)+p0(nz-pml+1:nz-2,3:pml).*(n1*(Tao_xx(nz-pml+1:nz-2,(3:pml)+1)-Tao_xx(nz-pml+1:nz-2,(3:pml)-1))/dx+...
                n2*(Tao_xx(nz-pml+1:nz-2,(3:pml)+2)-Tao_xx(nz-pml+1:nz-2,(3:pml)-2))/dx));
            Vx_3(nz-pml+1:nz-2,3:pml)=az(nz-pml+1:nz-2,3:pml).*(bz(nz-pml+1:nz-2,3:pml).*Vx_3(nz-pml+1:nz-2,3:pml)+p0(nz-pml+1:nz-2,3:pml).*(n1*(Tao_xz((nz-pml+1:nz-2)+1,3:pml)-Tao_xz((nz-pml+1:nz-2)-1,3:pml))/dz+...
                n2*(Tao_xz((nz-pml+1:nz-2)+2,3:pml)-Tao_xz((nz-pml+1:nz-2)-2,3:pml))/dz));
            Vx(nz-pml+1:nz-2,3:pml)=Vx_1(nz-pml+1:nz-2,3:pml)+Vx_3(nz-pml+1:nz-2,3:pml);
            %Vz
            Vz_1(nz-pml+1:nz-2,3:pml)=ax(nz-pml+1:nz-2,3:pml).*(bx(nz-pml+1:nz-2,3:pml).*Vz_1(nz-pml+1:nz-2,3:pml)+p0(nz-pml+1:nz-2,3:pml).*(n1*(Tao_xz(nz-pml+1:nz-2,(3:pml)+1)-Tao_xz(nz-pml+1:nz-2,(3:pml)-1))/dx+...
                n2*(Tao_xz(nz-pml+1:nz-2,(3:pml)+2)-Tao_xz(nz-pml+1:nz-2,(3:pml)-2))/dx));
            Vz_3(nz-pml+1:nz-2,3:pml)=az(nz-pml+1:nz-2,3:pml).*(bz(nz-pml+1:nz-2,3:pml).*Vz_3(nz-pml+1:nz-2,3:pml)+p0(nz-pml+1:nz-2,3:pml).*(n1*(Tao_zz((nz-pml+1:nz-2)+1,3:pml)-Tao_zz((nz-pml+1:nz-2)-1,3:pml))/dz+...
                n2*(Tao_zz((nz-pml+1:nz-2)+2,3:pml)-Tao_zz((nz-pml+1:nz-2)-2,3:pml))/dz));
            Vz(nz-pml+1:nz-2,3:pml)=Vz_1(nz-pml+1:nz-2,3:pml)+Vz_3(nz-pml+1:nz-2,3:pml);
        % 中下区域（仅Z方向PML吸收）
            % Vx速度分量更新
            Vx_1(nz-pml+1:nz-2,pml+1:nx-pml)=Vx_1(nz-pml+1:nz-2,pml+1:nx-pml)+p0(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Tao_xx(nz-pml+1:nz-2,(pml+1:nx-pml)+1)-Tao_xx(nz-pml+1:nz-2,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xx(nz-pml+1:nz-2,(pml+1:nx-pml)+2)-Tao_xx(nz-pml+1:nz-2,(pml+1:nx-pml)-2))/dx);
            Vx_3(nz-pml+1:nz-2,pml+1:nx-pml)=az(nz-pml+1:nz-2,pml+1:nx-pml).*(bz(nz-pml+1:nz-2,pml+1:nx-pml).*Vx_3(nz-pml+1:nz-2,pml+1:nx-pml)+p0(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Tao_xz((nz-pml+1:nz-2)+1,pml+1:nx-pml)-Tao_xz((nz-pml+1:nz-2)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_xz((nz-pml+1:nz-2)+2,pml+1:nx-pml)-Tao_xz((nz-pml+1:nz-2)-2,pml+1:nx-pml))/dz));
            Vx(nz-pml+1:nz-2,pml+1:nx-pml)=Vx_1(nz-pml+1:nz-2,pml+1:nx-pml)+Vx_3(nz-pml+1:nz-2,pml+1:nx-pml);
            %Vz
            Vz_1(nz-pml+1:nz-2,pml+1:nx-pml)=Vz_1(nz-pml+1:nz-2,pml+1:nx-pml)+p0(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Tao_xz(nz-pml+1:nz-2,(pml+1:nx-pml)+1)-Tao_xz(nz-pml+1:nz-2,(pml+1:nx-pml)-1))/dx+...
                n2*(Tao_xz(nz-pml+1:nz-2,(pml+1:nx-pml)+2)-Tao_xz(nz-pml+1:nz-2,(pml+1:nx-pml)-2))/dx);
            Vz_3(nz-pml+1:nz-2,pml+1:nx-pml)=az(nz-pml+1:nz-2,pml+1:nx-pml).*(bz(nz-pml+1:nz-2,pml+1:nx-pml).*Vz_3(nz-pml+1:nz-2,pml+1:nx-pml)+p0(nz-pml+1:nz-2,pml+1:nx-pml).*(n1*(Tao_zz((nz-pml+1:nz-2)+1,pml+1:nx-pml)-Tao_zz((nz-pml+1:nz-2)-1,pml+1:nx-pml))/dz+...
                n2*(Tao_zz((nz-pml+1:nz-2)+2,pml+1:nx-pml)-Tao_zz((nz-pml+1:nz-2)-2,pml+1:nx-pml))/dz));
            Vz(nz-pml+1:nz-2,pml+1:nx-pml)=Vz_1(nz-pml+1:nz-2,pml+1:nx-pml)+Vz_3(nz-pml+1:nz-2,pml+1:nx-pml);
        % 右下角区域（双向PML吸收）
        for count_j=nx-pml+1:nx-2
            kx=(count_j-(nx-pml+1))/pml;%nx-pml+1+2是起点
            w1=da*kx^M;
            ax(:,count_j)=1/(1+0.5*w1*dt);
            bx(:,count_j)=1-0.5*w1*dt;
        end
            %Vx
            Vx_1(nz-pml+1:nz-2,nx-pml+1:nx-2)=ax(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bx(nz-pml+1:nz-2,nx-pml+1:nx-2).*Vx_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+p0(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Tao_xx(nz-pml+1:nz-2,(nx-pml+1:nx-2)+1)-Tao_xx(nz-pml+1:nz-2,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xx(nz-pml+1:nz-2,(nx-pml+1:nx-2)+2)-Tao_xx(nz-pml+1:nz-2,(nx-pml+1:nx-2)-2))/dx));
            Vx_3(nz-pml+1:nz-2,nx-pml+1:nx-2)=az(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bz(nz-pml+1:nz-2,nx-pml+1:nx-2).*Vx_3(nz-pml+1:nz-2,nx-pml+1:nx-2)+p0(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Tao_xz((nz-pml+1:nz-2)+1,nx-pml+1:nx-2)-Tao_xz((nz-pml+1:nz-2)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_xz((nz-pml+1:nz-2)+2,nx-pml+1:nx-2)-Tao_xz((nz-pml+1:nz-2)-2,nx-pml+1:nx-2))/dz));
            Vx(nz-pml+1:nz-2,nx-pml+1:nx-2)=Vx_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+Vx_3(nz-pml+1:nz-2,nx-pml+1:nx-2);
            %Vz
            Vz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)=ax(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bx(nz-pml+1:nz-2,nx-pml+1:nx-2).*Vz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+p0(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Tao_xz(nz-pml+1:nz-2,(nx-pml+1:nx-2)+1)-Tao_xz(nz-pml+1:nz-2,(nx-pml+1:nx-2)-1))/dx+...
                n2*(Tao_xz(nz-pml+1:nz-2,(nx-pml+1:nx-2)+2)-Tao_xz(nz-pml+1:nz-2,(nx-pml+1:nx-2)-2))/dx));
            Vz_3(nz-pml+1:nz-2,nx-pml+1:nx-2)=az(nz-pml+1:nz-2,nx-pml+1:nx-2).*(bz(nz-pml+1:nz-2,nx-pml+1:nx-2).*Vz_3(nz-pml+1:nz-2,nx-pml+1:nx-2)+p0(nz-pml+1:nz-2,nx-pml+1:nx-2).*(n1*(Tao_zz((nz-pml+1:nz-2)+1,nx-pml+1:nx-2)-Tao_zz((nz-pml+1:nz-2)-1,nx-pml+1:nx-2))/dz+...
                n2*(Tao_zz((nz-pml+1:nz-2)+2,nx-pml+1:nx-2)-Tao_zz((nz-pml+1:nz-2)-2,nx-pml+1:nx-2))/dz));
            Vz(nz-pml+1:nz-2,nx-pml+1:nx-2)=Vz_1(nz-pml+1:nz-2,nx-pml+1:nx-2)+Vz_3(nz-pml+1:nz-2,nx-pml+1:nx-2);  

    % 3.3 FDTD计算进度实时显示
    if(mod(count_t,10)==0)
        disp('time points:');disp(count_t);disp('of');disp('source:');disp(count_s);
    end
    
%==========================================================================
%                        第四章：声波测井数据采集与输出
%==========================================================================
% 功能说明：实现声波测井数据的实时采集、可视化显示和数据存储，
% 包括检波器阵列数据采集、单炮数据整理和多炮数据存储。
%==========================================================================

%--------------------------------------------------------------------------
%                    4.1 波场实时可视化显示
%--------------------------------------------------------------------------

% 4.1.1 前三炮波场实时显示（用于调试和监控）
if(count_s<=3)
    if(mod(count_t,10)==0)
        figure(1);
        % 计算物理坐标轴
        x_axis = (0:nx-1) * dx;     % X方向物理坐标 [m]
        z_axis = (0:nz-1) * dz;     % Z方向物理坐标 [m]
        
        % 显示正应力场（按物理尺寸）
        imagesc(x_axis, z_axis, Tao_xx/10^6);  % 显示X方向正应力场（MPa单位）
        
        % 设置正确的纵横比例
        axis equal;                 % 等比例显示
        axis tight;                 % 紧凑坐标轴
        
        % 添加颜色条和标签
        colorbar;                   % 显示颜色条
        title(['第', num2str(count_s), '炮 时间步', num2str(count_t), ' 正应力场 (MPa)']);
        xlabel('X方向距离 (m)'); ylabel('Z方向距离 (m)');
        
        % 添加网格线（可选）
        grid on;
        
        drawnow;                    % 实时更新显示
    end
end

%--------------------------------------------------------------------------
%                    4.2 声波测井检波器阵列数据采集
%--------------------------------------------------------------------------

% 4.2.1 单时间步检波器数据采集
onep=zeros(N,1);                    % 单时间步N道数据缓存
cc=1;                               % 道数计数器

% 4.2.2 检波器阵列数据采集（从下向上按道顺序）
% 说明：检波器从震源上方开始，按照固定间距向上排列
for count_j=pos_s-len_StoR:-len_RtoR:pos_s-len_StoR-(N-1)*len_RtoR
    onep(cc)=Tao_xx(count_j,med_x); % 采集X方向正应力分量
    cc=cc+1;                        % 道数递增
end

% 4.2.3 单时间步数据存储到单炮矩阵
X(:,count_t)=onep;                  % 存储当前时间步的N道数据

end  % 结束时间步进循环

%--------------------------------------------------------------------------
%                    4.3 单炮数据整理与多炮数据存储
%--------------------------------------------------------------------------

% 4.3.1 单炮计算完成提示
disp('计算完炮数：');
disp(count_s);

% 4.3.2 单炮数据重组存储到总数据矩阵
% 说明：将N道×maxt时间点的单炮数据重组为一行存储
for count_i=1:1:N
    data(count_s,(count_i-1)*maxt+1:count_i*maxt)=X(count_i,:);
end

end  % 结束炮点循环

%==========================================================================
%                        数据保存与程序统计
%==========================================================================

% 获取程序执行时间
execution_time = toc;  % 记录总执行时间

% 生成数据文件名（包含时间戳）
current_time = char(datetime('now', 'Format', 'yyyyMMdd_HHmmss'));
data_filename = ['FDTD_SeismicLogging_', current_time, '.mat'];

% 保存所有重要数据到.mat文件
fprintf('正在保存数据到文件: %s\n', data_filename);
save(data_filename, ...
    'data', ...                    % 主要结果：多炮检波器数据
    'X', ...                      % 最后一炮的单炮数据
    'vp', 'vs', 'dens', ...       % 地层参数：速度和密度分布
    'lmd', 'miu', ...             % 弹性参数：Lamé常数和切变模量
    'p0', 'p1', 'p2', 'p3', ...   % FDTD系数矩阵
    'f', ...                      % Ricker子波震源函数
    'dx', 'dz', 'dt', ...         % 网格参数：空间步长和时间步长
    'nx', 'nz', 'maxt', ...       % 网格尺寸和时间步数
    'pml', ...                    % PML边界厚度
    'num_s', 'N', ...             % 炮数和检波器数量
    'len_StoR', 'len_RtoR', ...   % 源距和检波器间距
    'med_x', ...                  % 井孔中心X坐标
    'f0', 'cal', ...              % 震源频率和井径
    'po1', 'vp1', 'vs1', ...      % 井孔介质参数
    'po2', 'vp2', 'vs2', ...      % 地层介质参数
    'execution_time', ...         % 程序执行时间
    'current_time');              % 数据生成时间

% 显示保存信息
fprintf('数据保存完成！\n');
fprintf('文件名: %s\n', data_filename);
fprintf('文件大小: %.2f MB\n', dir(data_filename).bytes/1024/1024);
fprintf('总执行时间: %.2f 秒\n', execution_time);
fprintf('炮数: %d, 检波器数: %d, 时间步数: %d\n', num_s, N, maxt);
fprintf('数据矩阵尺寸: %d × %d\n', size(data,1), size(data,2));

% 显示数据调用示例
fprintf('\n=== 数据调用示例 ===\n');
fprintf('load(''%s'');  %% 加载数据\n', data_filename);
fprintf('imagesc(data);  %% 显示所有炮数据\n');
fprintf('plot(data(1,:));  %% 显示第1炮数据\n');
fprintf('plot(X(1,:));  %% 显示最后一炮第1道数据\n');
fprintf('========================\n'); 