% 显示多个炮点的检波器数据，并保存图片到Picture文件夹

%% ==================== 用户配置区域 ====================
% 1. 数据文件选择（在当前文件夹下）
% 方式1：指定具体文件名
data_filename = 'FDTD_SeismicLogging_20250729_152710.mat';
% 方式2：自动选择最新的.mat文件（注释掉上面一行，取消注释下面一行）
% data_filename = 'auto';

% 2. 要显示的炮点选择
% 方式1：指定具体炮号
shot_numbers = [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 67];
% 方式2：指定范围和间隔（例如：1:5:67 表示从第1炮到第67炮，每5炮取一个）
% shot_numbers = 1:5:67;
% 方式3：显示所有炮（注意：可能生成很多图片）
% shot_numbers = 1:67;
% 方式4：只显示单个炮点
% shot_numbers = 10;

% 3. 显示参数配置
display_config.trace_spacing = 1.5;        % 道间距（垂直偏移）
display_config.amplitude_scale = 0.8;      % 振幅缩放因子（0-1）
display_config.show_first_arrivals = true; % 是否标注首波到达
display_config.apply_agc = false;          % 是否应用自动增益控制
display_config.agc_window = 50;            % AGC窗口长度（时间点数）
display_config.time_range = [0, 0.004];    % 显示时间范围[开始, 结束]秒，空数组表示全部
display_config.fill_positive = true;       % 是否填充正振幅
display_config.fill_color = [0.8, 0.2, 0.2]; % 填充颜色 [R, G, B]

%% ==================== 数据加载 ====================
% 加载所需变量
if ~exist('data', 'var') || ~exist('X', 'var')
    % 获取当前文件夹
    current_folder = pwd;
    
    if strcmp(data_filename, 'auto')
        % 自动选择最新的.mat文件
        mat_files = dir(fullfile(current_folder, '*.mat'));
        if isempty(mat_files)
            error('当前文件夹下没有找到.mat数据文件');
        end
        % 按修改时间排序，选择最新的
        [~, idx] = max([mat_files.datenum]);
        data_filename = mat_files(idx).name;
        fprintf('自动选择最新的数据文件: %s\n', data_filename);
    end
    
    % 构建完整文件路径
    full_data_path = fullfile(current_folder, data_filename);
    
    % 尝试加载保存的数据
    try
        load(full_data_path);
        fprintf('成功加载数据文件: %s\n', full_data_path);
    catch ME
        fprintf('加载数据文件失败: %s\n', ME.message);
        fprintf('请检查文件名是否正确，或确保数据文件存在于当前文件夹: %s\n', current_folder);
        
        % 列出当前文件夹下的所有.mat文件
        mat_files = dir(fullfile(current_folder, '*.mat'));
        if ~isempty(mat_files)
            fprintf('\n当前文件夹下可用的.mat文件:\n');
            for i = 1:length(mat_files)
                fprintf('  %d. %s (修改时间: %s)\n', i, mat_files(i).name, mat_files(i).date);
            end
            fprintf('\n请修改程序开头的data_filename变量为正确的文件名\n');
        else
            fprintf('当前文件夹下没有找到任何.mat文件\n');
        end
        error('数据加载失败，程序终止');
    end
end

% 检查并定义必要的变量
if ~exist('nz', 'var') || ~exist('pml', 'var') || ~exist('len_RtoR', 'var') || ~exist('dz', 'var') || ~exist('num_receivers', 'var') || ~exist('receiver_positions', 'var') || ~exist('num_s', 'var')
    fprintf('警告: 一些必要的变量未定义，使用522.m中的默认值\n');
    
    % 使用522.m中的默认值
    if ~exist('dz', 'var')
        % 基于522.m中的计算，波长的十分之一
        dz = 0.015; % 522.m中的默认值，对应于波长的十分之一
        fprintf('设置dz = %.4f\n', dz);
    end
    
    if ~exist('pml', 'var')
        pml = 50; % 522.m中的默认值
        fprintf('设置pml = %d\n', pml);
    end
    
    if ~exist('nz', 'var')
        nz = 2*pml+1200; % 522.m中的计算方式
        fprintf('设置nz = %d\n', nz);
    end
    
    if ~exist('len_RtoR', 'var')
        if exist('L_RtoR', 'var')
            len_RtoR = fix(L_RtoR/dz); % 522.m中的计算方式
            fprintf('基于L_RtoR和dz计算len_RtoR = %d\n', len_RtoR);
        else
            len_RtoR = 10; % 522.m中的默认值
            fprintf('设置len_RtoR = %d\n', len_RtoR);
        end
    end
    
    % 检波器数量
    if ~exist('num_receivers', 'var')
        if exist('N', 'var')
            num_receivers = N; % 使用N变量（如果存在）
            fprintf('基于N变量设置num_receivers = %d\n', num_receivers);
        else
            num_receivers = 6; % 检波器数量为6
            fprintf('设置num_receivers = %d\n', num_receivers);
        end
    end
    
    % 炮数
    if ~exist('num_s', 'var')
        num_s = 67; % 根据522.m中的默认值
        fprintf('设置num_s = %d\n', num_s);
    end
    
    % 检波器位置
    if ~exist('receiver_positions', 'var')
        receiver_positions = zeros(num_receivers, 1);
        if exist('L_StoR', 'var')
            for i = 1:num_receivers
                receiver_positions(i) = L_StoR + (i-1) * 0.15; % 检波器间距0.15米
            end
            fprintf('基于L_StoR计算检波器位置\n');
        else
            for i = 1:num_receivers
                receiver_positions(i) = 1.5 + (i-1) * 0.15; % 第一个检波器1.5米，间距0.15米
            end
            fprintf('使用默认值计算检波器位置\n');
        end
    end
end

% 创建Picture文件夹（如果不存在）
picture_dir = fullfile(pwd, 'Picture');
if ~exist(picture_dir, 'dir')
    mkdir(picture_dir);
    fprintf('创建Picture文件夹: %s\n', picture_dir);
end

% 验证炮点选择
fprintf('\n用户配置:\n');
fprintf('数据文件: %s\n', data_filename);
fprintf('选择的炮点: ');
if length(shot_numbers) <= 10
    fprintf('%s\n', mat2str(shot_numbers));
else
    fprintf('共%d个炮点 (从%d到%d)\n', length(shot_numbers), min(shot_numbers), max(shot_numbers));
end

% 检查数据范围
if exist('X', 'var')
    max_shots_available = num_s;  % 使用num_s作为可用炮数
    fprintf('数据文件中可用的炮数: %d\n', max_shots_available);
    fprintf('检波器数据大小: %dx%d\n', size(X));
    
    % 过滤无效的炮点编号
    valid_shots = shot_numbers(shot_numbers >= 1 & shot_numbers <= max_shots_available);
    invalid_shots = shot_numbers(shot_numbers < 1 | shot_numbers > max_shots_available);
    
    if ~isempty(invalid_shots)
        fprintf('警告: 以下炮点超出数据范围，将被忽略: %s\n', mat2str(invalid_shots));
    end
    
    if isempty(valid_shots)
        error('没有有效的炮点可以处理，请检查shot_numbers配置');
    end
    
    % 更新炮点列表为有效的炮点
    shot_numbers = valid_shots;
    fprintf('实际将处理的炮点: ');
    if length(shot_numbers) <= 10
        fprintf('%s\n', mat2str(shot_numbers));
    else
        fprintf('共%d个炮点\n', length(shot_numbers));
    end
else
    fprintf('警告: 未找到X变量，无法验证炮点范围\n');
end

% 创建时间向量
time_vec = (1:maxt) * dt;

% 从配置中获取显示参数
offset = display_config.trace_spacing;
amplitude_scale = display_config.amplitude_scale;
show_first_arrivals = display_config.show_first_arrivals;
apply_agc = display_config.apply_agc;
agc_window = display_config.agc_window;
time_range = display_config.time_range;
fill_positive = display_config.fill_positive;
fill_color = display_config.fill_color;

% 确定时间显示范围
if isempty(time_range)
    time_indices = 1:maxt;
    display_time = time_vec;
else
    time_indices = find(time_vec >= time_range(1) & time_vec <= time_range(2));
    if isempty(time_indices)
        time_indices = 1:maxt;
    end
    display_time = time_vec(time_indices);
end

% 循环处理每个炮点
for shot_idx = 1:length(shot_numbers)
    % 当前炮点编号
    count_s = shot_numbers(shot_idx);
    
    % 检查炮点编号是否有效
    if count_s > size(data, 1)
        fprintf('警告: 炮点 %d 超出数据范围，跳过\n', count_s);
        continue;
    end
    
    % 计算激发点位置（与522.m中相同的计算方法）
    pos_s = nz - 3*pml - (count_s-1) * len_RtoR;
    
    % 输出炮点信息
    fprintf('\n============ 第%d个炮点信息 ============\n', count_s);
    fprintf('激发点位置（网格坐标）: %d\n', pos_s);
    fprintf('激发点深度（米）: %.2f\n', pos_s * dz);
    
    % 创建新图形
    figure('Position', [100, 100, 800, 600], 'Color', 'w', 'Renderer', 'painters');
    
    % 清除当前图形并准备绘图
    cla;
    hold on;
    
    % 绘制每个检波器的数据
    for i = 1:num_receivers
        % 检波器波形偏移位置
        trace_offset = i * offset;
        
        % 绘制检波器数据（黑色实线）
        if exist('data', 'var') && i <= num_receivers
            % 从 data 变量中提取当前炮点的检波器数据
            % data矩阵结构：(炮数, 道数×时间点)
            % 第count_s炮的第i道数据存储在：data(count_s, (i-1)*maxt+1:i*maxt)
            orig_plot_data = data(count_s, (i-1)*maxt+1:i*maxt);
            
            % 归一化处理
            if ~exist('max_amplitude_for_shot', 'var') || isempty(max_amplitude_for_shot) || length(max_amplitude_for_shot) < count_s
                max_amplitude_for_shot(count_s) = 0;
                for j = 1:num_receivers
                    % 从data矩阵中提取当前炮点的第j道数据
                    temp_data = data(count_s, (j-1)*maxt+1:j*maxt);
                    max_amplitude_for_shot(count_s) = max(max_amplitude_for_shot(count_s), max(abs(temp_data)));
                end
            end
            
            % 如果最大值非常小，使用单个波形的最大值
            if max_amplitude_for_shot(count_s) < 1e-10
                norm_factor = max(abs(orig_plot_data));
                if norm_factor < 1e-10
                    norm_factor = 1;
                end
            else
                norm_factor = max_amplitude_for_shot(count_s);
            end
            
            orig_norm_data = orig_plot_data / norm_factor;
            h1 = plot(display_time, orig_norm_data(time_indices) + trace_offset, 'k-', 'LineWidth', 2);
        end
    end
    
    % 设置图形属性
    xlabel('时间 (秒)', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('检波器', 'FontSize', 14, 'FontWeight', 'bold');
    title(['第', num2str(count_s), '个炮点的检波器偏移图'], 'FontSize', 16, 'FontWeight', 'bold');
    grid on;
    % 增强轴线及网格线的可见度
    set(gca, 'LineWidth', 1.5, 'GridLineStyle', '-', 'GridAlpha', 0.15);
    % 设置轴标签字体
    set(gca, 'FontSize', 12, 'FontWeight', 'bold');
    
    % 设置 Y 轴刻度
    yticks(offset:offset:num_receivers*offset);
    yticklabels(1:num_receivers);
    
    % 调整图形范围
    xlim([0, time_vec(end)]);
    ylim([0, (num_receivers+0.5)*offset]);
    
    % 保存图形到Picture文件夹
fig_filename = fullfile(picture_dir, sprintf('炮点_%d.png', count_s));
saveas(gcf, fig_filename);
fprintf('保存图片: %s\n', fig_filename);

% 关闭当前图形窗口以释放内存
close(gcf);
end

fprintf('\n所有图片已保存到: %s\n', picture_dir);

fprintf('\n程序执行完成！\n');
fprintf('共处理了 %d 个炮点，每个炮点显示 %d 个检波器的波形\n', length(shot_numbers), num_receivers);
